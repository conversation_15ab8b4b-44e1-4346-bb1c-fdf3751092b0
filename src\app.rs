use eframe::egui;
use std::sync::{Arc, Mutex};
use std::path::PathBuf;
use crate::scanner::{DiskScanner, ScanResult};
use crate::cleaner::DiskCleaner;

/// 主应用程序结构
pub struct DiskCleanerApp {
    /// 磁盘扫描器
    scanner: Arc<Mutex<DiskScanner>>,
    /// 磁盘清理器
    cleaner: DiskCleaner,
    /// 当前扫描结果
    scan_result: Option<ScanResult>,
    /// 是否正在扫描
    is_scanning: bool,
    /// 选中的扫描路径
    selected_path: String,
    /// 界面状态
    ui_state: UiState,
}

#[derive(Default)]
struct UiState {
    /// 当前选中的标签页
    current_tab: TabType,
    /// 是否显示隐藏文件
    show_hidden_files: bool,
    /// 最小文件大小过滤器（MB）
    min_file_size_mb: f32,
    /// 搜索关键词
    search_keyword: String,
}

#[derive(Default, PartialEq)]
enum TabType {
    #[default]
    Analysis,    // 磁盘分析
    Cleanup,     // 清理工具
    Settings,    // 设置
}

impl DiskCleanerApp {
    pub fn new(cc: &eframe::CreationContext<'_>) -> Self {
        // 设置深色主题
        cc.egui_ctx.set_visuals(egui::Visuals::dark());
        
        Self {
            scanner: Arc::new(Mutex::new(DiskScanner::new())),
            cleaner: DiskCleaner::new(),
            scan_result: None,
            is_scanning: false,
            selected_path: "C:\\".to_string(),
            ui_state: UiState::default(),
        }
    }
    
    /// 开始扫描磁盘
    fn start_scan(&mut self) {
        if self.is_scanning {
            return;
        }
        
        self.is_scanning = true;
        self.scan_result = None;
        
        // 这里后续会实现异步扫描
        log::info!("开始扫描路径: {}", self.selected_path);
    }
    
    /// 渲染顶部工具栏
    fn render_toolbar(&mut self, ui: &mut egui::Ui) {
        ui.horizontal(|ui| {
            ui.label("扫描路径:");
            ui.text_edit_singleline(&mut self.selected_path);
            
            if ui.button("📁 选择文件夹").clicked() {
                // 后续实现文件夹选择对话框
            }
            
            ui.separator();
            
            if self.is_scanning {
                ui.add(egui::Spinner::new());
                ui.label("扫描中...");
                if ui.button("停止").clicked() {
                    self.is_scanning = false;
                }
            } else {
                if ui.button("🔍 开始扫描").clicked() {
                    self.start_scan();
                }
            }
        });
    }
    
    /// 渲染标签页
    fn render_tabs(&mut self, ui: &mut egui::Ui) {
        ui.horizontal(|ui| {
            ui.selectable_value(&mut self.ui_state.current_tab, TabType::Analysis, "📊 磁盘分析");
            ui.selectable_value(&mut self.ui_state.current_tab, TabType::Cleanup, "🧹 清理工具");
            ui.selectable_value(&mut self.ui_state.current_tab, TabType::Settings, "⚙️ 设置");
        });
        ui.separator();
    }
    
    /// 渲染磁盘分析页面
    fn render_analysis_tab(&mut self, ui: &mut egui::Ui) {
        ui.horizontal(|ui| {
            // 左侧：文件夹树状图
            ui.group(|ui| {
                ui.vertical(|ui| {
                    ui.heading("文件夹结构");
                    ui.separator();
                    
                    egui::ScrollArea::vertical()
                        .max_height(500.0)
                        .show(ui, |ui| {
                            if let Some(ref result) = self.scan_result {
                                self.render_folder_tree(ui, result);
                            } else {
                                ui.label("请先开始扫描");
                            }
                        });
                });
            });
            
            ui.separator();
            
            // 右侧：统计信息和可视化
            ui.group(|ui| {
                ui.vertical(|ui| {
                    ui.heading("磁盘使用统计");
                    ui.separator();
                    
                    if let Some(ref result) = self.scan_result {
                        self.render_statistics(ui, result);
                    } else {
                        ui.label("暂无数据");
                    }
                });
            });
        });
    }
    
    /// 渲染文件夹树状图
    fn render_folder_tree(&self, ui: &mut egui::Ui, _result: &ScanResult) {
        // 临时占位符，后续实现真实的树状图
        ui.label("文件夹树状图 (开发中...)");
        ui.label("📁 C:\\");
        ui.label("  📁 Program Files (2.5 GB)");
        ui.label("  📁 Windows (15.2 GB)");
        ui.label("  📁 Users (8.7 GB)");
        ui.label("  📁 Temp (1.2 GB)");
    }
    
    /// 渲染统计信息
    fn render_statistics(&self, ui: &mut egui::Ui, _result: &ScanResult) {
        // 临时占位符，后续实现真实的统计
        ui.label("总大小: 27.6 GB");
        ui.label("文件数量: 125,432");
        ui.label("文件夹数量: 8,765");
        ui.separator();
        ui.label("最大文件:");
        ui.label("  📄 large_file.zip (500 MB)");
        ui.label("最大文件夹:");
        ui.label("  📁 Windows\\System32 (8.2 GB)");
    }
    
    /// 渲染清理工具页面
    fn render_cleanup_tab(&mut self, ui: &mut egui::Ui) {
        ui.heading("系统清理工具");
        ui.separator();
        
        ui.group(|ui| {
            ui.label("🗑️ 临时文件清理");
            ui.horizontal(|ui| {
                if ui.button("扫描临时文件").clicked() {
                    // 后续实现
                }
                ui.label("预计可清理: 计算中...");
            });
        });
        
        ui.group(|ui| {
            ui.label("🌐 浏览器缓存清理");
            ui.horizontal(|ui| {
                if ui.button("扫描浏览器缓存").clicked() {
                    // 后续实现
                }
                ui.label("预计可清理: 计算中...");
            });
        });
        
        ui.group(|ui| {
            ui.label("📦 系统日志清理");
            ui.horizontal(|ui| {
                if ui.button("扫描系统日志").clicked() {
                    // 后续实现
                }
                ui.label("预计可清理: 计算中...");
            });
        });
    }
    
    /// 渲染设置页面
    fn render_settings_tab(&mut self, ui: &mut egui::Ui) {
        ui.heading("设置");
        ui.separator();
        
        ui.checkbox(&mut self.ui_state.show_hidden_files, "显示隐藏文件");
        
        ui.horizontal(|ui| {
            ui.label("最小文件大小过滤 (MB):");
            ui.add(egui::Slider::new(&mut self.ui_state.min_file_size_mb, 0.0..=1000.0));
        });
        
        ui.horizontal(|ui| {
            ui.label("搜索关键词:");
            ui.text_edit_singleline(&mut self.ui_state.search_keyword);
        });
    }
}

impl eframe::App for DiskCleanerApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        egui::CentralPanel::default().show(ctx, |ui| {
            // 渲染工具栏
            self.render_toolbar(ui);
            ui.separator();
            
            // 渲染标签页
            self.render_tabs(ui);
            
            // 根据当前标签页渲染对应内容
            match self.ui_state.current_tab {
                TabType::Analysis => self.render_analysis_tab(ui),
                TabType::Cleanup => self.render_cleanup_tab(ui),
                TabType::Settings => self.render_settings_tab(ui),
            }
        });
        
        // 如果正在扫描，定期刷新界面
        if self.is_scanning {
            ctx.request_repaint();
        }
    }
}
