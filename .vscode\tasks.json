{"version": "2.0.0", "tasks": [{"label": "cargo build", "type": "shell", "command": "cargo", "args": ["build"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$rustc"]}, {"label": "cargo run", "type": "shell", "command": "cargo", "args": ["run"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$rustc"]}, {"label": "cargo build --release", "type": "shell", "command": "cargo", "args": ["build", "--release"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$rustc"]}, {"label": "cargo test", "type": "shell", "command": "cargo", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$rustc"]}, {"label": "cargo check", "type": "shell", "command": "cargo", "args": ["check"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$rustc"]}]}