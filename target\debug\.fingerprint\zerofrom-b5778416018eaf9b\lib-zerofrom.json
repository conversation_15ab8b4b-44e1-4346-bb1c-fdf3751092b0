{"rustc": 3926191382657067107, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 15657897354478470176, "path": 10090941507026976838, "deps": [[4022439902832367970, "zerofrom_derive", false, 1978126567040215836]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-b5778416018eaf9b\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}