{"rustc": 3926191382657067107, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"parsing\", \"printing\", \"proc-macro\", \"visit\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 874823998178671733, "deps": [[1988483478007900009, "unicode_ident", false, 8168668941875096033], [3060637413840920116, "proc_macro2", false, 11783807764890264669], [17990358020177143287, "quote", false, 2178856385441165900]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-6462da0675d6d17e\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}