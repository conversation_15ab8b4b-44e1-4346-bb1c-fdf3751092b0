use std::path::{Path, PathBuf};
use std::collections::HashMap;
use walkdir::WalkDir;
use serde::{Deserialize, Serialize};
use anyhow::Result;

/// 磁盘扫描器
pub struct DiskScanner {
    /// 是否包含隐藏文件
    include_hidden: bool,
    /// 最大扫描深度
    max_depth: usize,
    /// 最小文件大小过滤器（字节）
    min_file_size: u64,
}

/// 扫描结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScanResult {
    /// 扫描的根路径
    pub root_path: PathBuf,
    /// 总大小（字节）
    pub total_size: u64,
    /// 文件数量
    pub file_count: u64,
    /// 文件夹数量
    pub folder_count: u64,
    /// 文件夹信息
    pub folders: Vec<FolderInfo>,
    /// 大文件列表（超过指定大小的文件）
    pub large_files: Vec<FileInfo>,
    /// 扫描耗时（毫秒）
    pub scan_duration_ms: u64,
}

/// 文件夹信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FolderInfo {
    /// 文件夹路径
    pub path: PathBuf,
    /// 文件夹名称
    pub name: String,
    /// 文件夹大小（字节）
    pub size: u64,
    /// 文件数量
    pub file_count: u64,
    /// 子文件夹数量
    pub subfolder_count: u64,
    /// 相对于根目录的深度
    pub depth: usize,
    /// 父文件夹路径
    pub parent_path: Option<PathBuf>,
}

/// 文件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileInfo {
    /// 文件路径
    pub path: PathBuf,
    /// 文件名
    pub name: String,
    /// 文件大小（字节）
    pub size: u64,
    /// 文件扩展名
    pub extension: Option<String>,
    /// 最后修改时间
    pub modified_time: Option<chrono::DateTime<chrono::Utc>>,
    /// 是否为隐藏文件
    pub is_hidden: bool,
}

impl DiskScanner {
    /// 创建新的磁盘扫描器
    pub fn new() -> Self {
        Self {
            include_hidden: false,
            max_depth: 10,
            min_file_size: 0,
        }
    }
    
    /// 设置是否包含隐藏文件
    pub fn include_hidden(mut self, include: bool) -> Self {
        self.include_hidden = include;
        self
    }
    
    /// 设置最大扫描深度
    pub fn max_depth(mut self, depth: usize) -> Self {
        self.max_depth = depth;
        self
    }
    
    /// 设置最小文件大小过滤器
    pub fn min_file_size(mut self, size: u64) -> Self {
        self.min_file_size = size;
        self
    }
    
    /// 扫描指定路径
    pub fn scan<P: AsRef<Path>>(&self, path: P) -> Result<ScanResult> {
        let start_time = std::time::Instant::now();
        let root_path = path.as_ref().to_path_buf();
        
        log::info!("开始扫描路径: {:?}", root_path);
        
        let mut total_size = 0u64;
        let mut file_count = 0u64;
        let mut folder_count = 0u64;
        let mut folders: HashMap<PathBuf, FolderInfo> = HashMap::new();
        let mut large_files = Vec::new();
        
        // 使用walkdir遍历目录
        for entry in WalkDir::new(&root_path)
            .max_depth(self.max_depth)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            let path = entry.path();
            let metadata = match entry.metadata() {
                Ok(meta) => meta,
                Err(_) => continue,
            };
            
            // 检查是否为隐藏文件
            if !self.include_hidden && self.is_hidden_file(path) {
                continue;
            }
            
            if metadata.is_file() {
                let file_size = metadata.len();
                
                // 应用文件大小过滤器
                if file_size < self.min_file_size {
                    continue;
                }
                
                total_size += file_size;
                file_count += 1;
                
                // 收集大文件信息（超过100MB的文件）
                if file_size > 100 * 1024 * 1024 {
                    let file_info = FileInfo {
                        path: path.to_path_buf(),
                        name: path.file_name()
                            .unwrap_or_default()
                            .to_string_lossy()
                            .to_string(),
                        size: file_size,
                        extension: path.extension()
                            .map(|ext| ext.to_string_lossy().to_string()),
                        modified_time: metadata.modified()
                            .ok()
                            .and_then(|time| chrono::DateTime::from(time).into()),
                        is_hidden: self.is_hidden_file(path),
                    };
                    large_files.push(file_info);
                }
                
                // 更新父文件夹信息
                if let Some(parent) = path.parent() {
                    let folder_info = folders.entry(parent.to_path_buf())
                        .or_insert_with(|| FolderInfo {
                            path: parent.to_path_buf(),
                            name: parent.file_name()
                                .unwrap_or_default()
                                .to_string_lossy()
                                .to_string(),
                            size: 0,
                            file_count: 0,
                            subfolder_count: 0,
                            depth: parent.components().count(),
                            parent_path: parent.parent().map(|p| p.to_path_buf()),
                        });
                    
                    folder_info.size += file_size;
                    folder_info.file_count += 1;
                }
            } else if metadata.is_dir() {
                folder_count += 1;
                
                // 确保文件夹信息存在
                folders.entry(path.to_path_buf())
                    .or_insert_with(|| FolderInfo {
                        path: path.to_path_buf(),
                        name: path.file_name()
                            .unwrap_or_default()
                            .to_string_lossy()
                            .to_string(),
                        size: 0,
                        file_count: 0,
                        subfolder_count: 0,
                        depth: path.components().count(),
                        parent_path: path.parent().map(|p| p.to_path_buf()),
                    });
            }
        }
        
        // 计算子文件夹数量
        for folder_info in folders.values_mut() {
            folder_info.subfolder_count = folders.values()
                .filter(|f| f.parent_path.as_ref() == Some(&folder_info.path))
                .count() as u64;
        }
        
        // 按大小排序大文件
        large_files.sort_by(|a, b| b.size.cmp(&a.size));
        
        // 按大小排序文件夹
        let mut folders: Vec<FolderInfo> = folders.into_values().collect();
        folders.sort_by(|a, b| b.size.cmp(&a.size));
        
        let scan_duration_ms = start_time.elapsed().as_millis() as u64;
        
        log::info!(
            "扫描完成: {} 个文件, {} 个文件夹, 总大小 {} 字节, 耗时 {} 毫秒",
            file_count, folder_count, total_size, scan_duration_ms
        );
        
        Ok(ScanResult {
            root_path,
            total_size,
            file_count,
            folder_count,
            folders,
            large_files,
            scan_duration_ms,
        })
    }
    
    /// 检查是否为隐藏文件
    #[cfg(windows)]
    fn is_hidden_file<P: AsRef<Path>>(&self, path: P) -> bool {
        use std::os::windows::fs::MetadataExt;
        
        if let Ok(metadata) = path.as_ref().metadata() {
            const FILE_ATTRIBUTE_HIDDEN: u32 = 0x2;
            (metadata.file_attributes() & FILE_ATTRIBUTE_HIDDEN) != 0
        } else {
            false
        }
    }
    
    /// 检查是否为隐藏文件（非Windows平台）
    #[cfg(not(windows))]
    fn is_hidden_file<P: AsRef<Path>>(&self, path: P) -> bool {
        path.as_ref()
            .file_name()
            .and_then(|name| name.to_str())
            .map(|name| name.starts_with('.'))
            .unwrap_or(false)
    }
}

impl Default for DiskScanner {
    fn default() -> Self {
        Self::new()
    }
}

/// 格式化文件大小为人类可读的字符串
pub fn format_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.2} {}", size, UNITS[unit_index])
    }
}
