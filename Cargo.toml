[package]
name = "c-disk-cleaner"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "一个轻量级的C盘空间分析与清理工具"
license = "MIT"

[dependencies]
# GUI框架
eframe = "0.24"
egui = "0.24"
egui_extras = "0.24"

# 文件系统操作
walkdir = "2.4"
sysinfo = "0.29"

# 数据序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 日志
log = "0.4"
env_logger = "0.10"

# Windows API (仅Windows平台)
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = [
    "winbase",
    "fileapi",
    "handleapi",
    "winnt",
    "errhandlingapi",
    "processthreadsapi",
    "sysinfoapi",
] }

[profile.release]
# 优化发布版本
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

[[bin]]
name = "c-disk-cleaner"
path = "src/main.rs"
