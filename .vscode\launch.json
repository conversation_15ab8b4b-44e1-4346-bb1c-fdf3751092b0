{"version": "0.2.0", "configurations": [{"type": "lldb", "request": "launch", "name": "Debug executable 'c-disk-cleaner'", "cargo": {"args": ["build", "--bin=c-disk-cleaner", "--package=c-disk-cleaner"], "filter": {"name": "c-disk-cleaner", "kind": "bin"}}, "args": [], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "sourceLanguages": ["rust"]}, {"type": "lldb", "request": "launch", "name": "Debug unit tests", "cargo": {"args": ["test", "--no-run", "--bin=c-disk-cleaner", "--package=c-disk-cleaner"], "filter": {"name": "c-disk-cleaner", "kind": "bin"}}, "args": [], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "sourceLanguages": ["rust"]}]}