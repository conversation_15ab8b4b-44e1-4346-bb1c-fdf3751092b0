{"rust-analyzer.cargo.features": "all", "rust-analyzer.checkOnSave.command": "check", "rust-analyzer.checkOnSave.allTargets": false, "rust-analyzer.cargo.loadOutDirsFromCheck": true, "rust-analyzer.procMacro.enable": true, "rust-analyzer.diagnostics.enable": true, "rust-analyzer.diagnostics.enableExperimental": true, "rust-analyzer.completion.addCallParentheses": true, "rust-analyzer.completion.addCallArgumentSnippets": true, "rust-analyzer.inlayHints.enable": true, "rust-analyzer.inlayHints.chainingHints": true, "rust-analyzer.inlayHints.parameterHints": true, "rust-analyzer.inlayHints.typeHints": true, "files.associations": {"*.rs": "rust"}, "editor.formatOnSave": true, "editor.defaultFormatter": "rust-lang.rust-analyzer", "[rust]": {"editor.defaultFormatter": "rust-lang.rust-analyzer", "editor.formatOnSave": true}}