{"rustc": 3926191382657067107, "features": "[\"default\", \"simd\", \"simd-adler32\", \"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 11250625435679592442, "path": 7507438735967364308, "deps": [[4018467389006652250, "simd_adler32", false, 13567891266928367164], [7911289239703230891, "adler2", false, 16093731195897456957]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\miniz_oxide-b2fcdb26d089bb2c\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}