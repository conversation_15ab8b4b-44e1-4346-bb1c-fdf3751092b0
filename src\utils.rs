use std::path::Path;
use std::time::{SystemTime, UNIX_EPOCH};

/// 工具函数模块

/// 格式化文件大小为人类可读的字符串
pub fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB", "PB"];
    const THRESHOLD: f64 = 1024.0;
    
    if size == 0 {
        return "0 B".to_string();
    }
    
    let mut size = size as f64;
    let mut unit_index = 0;
    
    while size >= THRESHOLD && unit_index < UNITS.len() - 1 {
        size /= THRESHOLD;
        unit_index += 1;
    }
    
    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.2} {}", size, UNITS[unit_index])
    }
}

/// 格式化时间戳为可读字符串
pub fn format_timestamp(timestamp: SystemTime) -> String {
    match timestamp.duration_since(UNIX_EPOCH) {
        Ok(duration) => {
            let secs = duration.as_secs();
            let datetime = chrono::DateTime::from_timestamp(secs as i64, 0)
                .unwrap_or_default();
            datetime.format("%Y-%m-%d %H:%M:%S").to_string()
        }
        Err(_) => "未知时间".to_string(),
    }
}

/// 格式化持续时间为可读字符串
pub fn format_duration(duration_ms: u64) -> String {
    if duration_ms < 1000 {
        format!("{} 毫秒", duration_ms)
    } else if duration_ms < 60_000 {
        format!("{:.1} 秒", duration_ms as f64 / 1000.0)
    } else if duration_ms < 3_600_000 {
        format!("{:.1} 分钟", duration_ms as f64 / 60_000.0)
    } else {
        format!("{:.1} 小时", duration_ms as f64 / 3_600_000.0)
    }
}

/// 获取文件扩展名
pub fn get_file_extension(path: &Path) -> Option<String> {
    path.extension()
        .and_then(|ext| ext.to_str())
        .map(|ext| ext.to_lowercase())
}

/// 检查路径是否安全（防止删除重要系统文件）
pub fn is_safe_to_delete(path: &Path) -> bool {
    let path_str = path.to_string_lossy().to_lowercase();
    
    // 危险路径列表
    let dangerous_paths = [
        "c:\\windows\\system32",
        "c:\\windows\\syswow64",
        "c:\\program files",
        "c:\\program files (x86)",
        "c:\\users\\<USER>\\boot",
        "c:\\recovery",
    ];
    
    // 检查是否为危险路径
    for dangerous_path in &dangerous_paths {
        if path_str.starts_with(dangerous_path) {
            return false;
        }
    }
    
    // 检查是否为系统文件
    if let Some(file_name) = path.file_name() {
        let file_name_str = file_name.to_string_lossy().to_lowercase();
        let system_files = [
            "ntldr",
            "bootmgr",
            "pagefile.sys",
            "hiberfil.sys",
            "swapfile.sys",
        ];
        
        if system_files.contains(&file_name_str.as_str()) {
            return false;
        }
    }
    
    true
}

/// 计算文件夹占用百分比
pub fn calculate_percentage(part: u64, total: u64) -> f32 {
    if total == 0 {
        0.0
    } else {
        (part as f64 / total as f64 * 100.0) as f32
    }
}

/// 获取磁盘可用空间
pub fn get_disk_free_space(path: &Path) -> Option<u64> {
    // 使用sysinfo库获取磁盘信息
    use sysinfo::{System, SystemExt, DiskExt};
    
    let mut system = System::new();
    system.refresh_disks();
    
    for disk in system.disks() {
        if path.starts_with(disk.mount_point()) {
            return Some(disk.available_space());
        }
    }
    
    None
}

/// 获取磁盘总空间
pub fn get_disk_total_space(path: &Path) -> Option<u64> {
    use sysinfo::{System, SystemExt, DiskExt};
    
    let mut system = System::new();
    system.refresh_disks();
    
    for disk in system.disks() {
        if path.starts_with(disk.mount_point()) {
            return Some(disk.total_space());
        }
    }
    
    None
}

/// 验证路径是否存在且可访问
pub fn validate_path(path: &Path) -> Result<(), String> {
    if !path.exists() {
        return Err(format!("路径不存在: {:?}", path));
    }
    
    if !path.is_dir() {
        return Err(format!("路径不是文件夹: {:?}", path));
    }
    
    // 尝试读取目录以检查权限
    match std::fs::read_dir(path) {
        Ok(_) => Ok(()),
        Err(e) => Err(format!("无法访问路径 {:?}: {}", path, e)),
    }
}

/// 生成唯一的临时文件名
pub fn generate_temp_filename(prefix: &str, extension: &str) -> String {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs();
    
    format!("{}_{}.{}", prefix, timestamp, extension)
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(0), "0 B");
        assert_eq!(format_file_size(512), "512 B");
        assert_eq!(format_file_size(1024), "1.00 KB");
        assert_eq!(format_file_size(1536), "1.50 KB");
        assert_eq!(format_file_size(1048576), "1.00 MB");
        assert_eq!(format_file_size(1073741824), "1.00 GB");
    }
    
    #[test]
    fn test_calculate_percentage() {
        assert_eq!(calculate_percentage(0, 100), 0.0);
        assert_eq!(calculate_percentage(50, 100), 50.0);
        assert_eq!(calculate_percentage(100, 100), 100.0);
        assert_eq!(calculate_percentage(25, 0), 0.0);
    }
    
    #[test]
    fn test_format_duration() {
        assert_eq!(format_duration(500), "500 毫秒");
        assert_eq!(format_duration(1500), "1.5 秒");
        assert_eq!(format_duration(65000), "1.1 分钟");
        assert_eq!(format_duration(3700000), "1.0 小时");
    }
}
