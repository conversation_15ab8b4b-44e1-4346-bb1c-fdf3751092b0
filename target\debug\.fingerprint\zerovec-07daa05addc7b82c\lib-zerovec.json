{"rustc": 3926191382657067107, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2241668132362809309, "path": 6959659584448961298, "deps": [[9620753569207166497, "zerovec_derive", false, 8970517473874531882], [10706449961930108323, "yoke", false, 11942729242328612818], [17046516144589451410, "zerofrom", false, 10244394911724120210]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-07daa05addc7b82c\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}