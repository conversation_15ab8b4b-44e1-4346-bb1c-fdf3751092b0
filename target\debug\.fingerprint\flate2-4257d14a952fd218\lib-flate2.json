{"rustc": 3926191382657067107, "features": "[\"any_impl\", \"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2241668132362809309, "path": 3519109377191498279, "deps": [[7312356825837975969, "crc32fast", false, 7284106837865484461], [7636735136738807108, "miniz_oxide", false, 12303105806499335791]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-4257d14a952fd218\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}