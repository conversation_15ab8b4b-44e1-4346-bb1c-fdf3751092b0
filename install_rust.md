# Rust 安装指南

## 方法一：自动安装（推荐）

1. **下载 Rust 安装器**
   - 访问官网：https://rustup.rs/
   - 或直接下载：https://win.rustup.rs/x86_64

2. **运行安装器**
   - 双击下载的 `rustup-init.exe`
   - 选择默认安装选项（直接按回车）
   - 等待安装完成

3. **验证安装**
   ```powershell
   # 重新打开 PowerShell 或 VSCode 终端
   rustc --version
   cargo --version
   ```

## 方法二：使用 PowerShell 安装

在 PowerShell 中运行以下命令：

```powershell
# 下载并运行 Rust 安装器
Invoke-WebRequest -Uri "https://win.rustup.rs/x86_64" -OutFile "rustup-init.exe"
.\rustup-init.exe
```

## 方法三：使用 Chocolatey（如果已安装）

```powershell
choco install rust
```

## 安装完成后的配置

### 1. 重启终端
安装完成后，需要重新打开 VSCode 或 PowerShell 终端，以便环境变量生效。

### 2. 验证安装
```powershell
rustc --version    # 应该显示 Rust 编译器版本
cargo --version    # 应该显示 Cargo 包管理器版本
```

### 3. 安装必要的组件
```powershell
# 安装 Windows 构建工具（如果需要）
rustup component add rust-src
rustup component add rust-analysis
rustup component add rls
```

## VSCode 扩展安装

安装完 Rust 后，在 VSCode 中安装以下扩展：

1. **rust-analyzer** (推荐)
   - 提供智能代码补全、错误检查等功能
   - 扩展 ID: `rust-lang.rust-analyzer`

2. **CodeLLDB** (调试器)
   - 提供 Rust 程序调试功能
   - 扩展 ID: `vadimcn.vscode-lldb`

3. **Better TOML** (可选)
   - 提供 Cargo.toml 文件语法高亮
   - 扩展 ID: `bungcip.better-toml`

## 安装后测试项目

1. **检查 Rust 环境**
   ```powershell
   cd "h:\自做工具\C盘清理"
   cargo --version
   ```

2. **检查项目依赖**
   ```powershell
   cargo check
   ```

3. **编译项目**
   ```powershell
   cargo build
   ```

4. **运行项目**
   ```powershell
   cargo run
   ```

## 常见问题解决

### 问题1：找不到 MSVC 构建工具
**解决方案**：
- 安装 Visual Studio Build Tools
- 或安装完整的 Visual Studio Community
- 或使用 GNU 工具链：`rustup default stable-x86_64-pc-windows-gnu`

### 问题2：网络连接问题
**解决方案**：
- 配置代理（如果在公司网络）
- 使用国内镜像源

### 问题3：权限问题
**解决方案**：
- 以管理员身份运行 PowerShell
- 或安装到用户目录

## 下一步

安装完成后，你就可以：
1. 在 VSCode 中打开项目
2. 使用 `Ctrl+Shift+P` → "Tasks: Run Task" → 选择 "cargo build"
3. 或直接在终端运行 `cargo run` 启动程序

## 性能优化建议

安装完成后，建议进行以下配置：

```powershell
# 设置 Rust 编译缓存目录（可选）
$env:CARGO_TARGET_DIR = "D:\rust_cache"  # 使用更快的磁盘

# 启用并行编译
$env:CARGO_BUILD_JOBS = "4"  # 根据 CPU 核心数调整
```

这些设置可以加快编译速度。
