# C盘清理工具

一个使用 Rust + egui 开发的轻量级、高性能的C盘空间分析与清理工具。

## 功能特性

### 🔍 磁盘空间分析
- **可视化展示**: 树状图和饼图展示磁盘占用情况
- **详细统计**: 文件数量、文件夹数量、总大小统计
- **大文件检测**: 自动识别占用空间较大的文件
- **实时扫描**: 快速扫描指定目录的磁盘使用情况

### 🧹 智能清理功能
- **安全清理**: 支持移动到回收站，避免误删重要文件
- **多种清理规则**:
  - Windows临时文件清理
  - 浏览器缓存清理
  - 系统日志文件清理
  - 自定义清理规则
- **批量操作**: 支持批量选择和清理文件

### ⚙️ 高级设置
- **过滤选项**: 按文件大小、文件类型、修改时间过滤
- **隐藏文件**: 可选择是否显示隐藏文件
- **扫描深度**: 可配置最大扫描深度
- **安全模式**: 可开启安全模式防止误删系统文件

## 技术特点

- **高性能**: 使用 Rust 开发，扫描速度快，内存占用低
- **轻量级**: 编译后单个 exe 文件，体积小（约 5-15MB）
- **跨平台**: 支持 Windows、macOS、Linux
- **现代界面**: 使用 egui 构建的现代化用户界面
- **安全可靠**: 内存安全，不会崩溃，支持安全删除

## 开发环境要求

### 必需软件
- **Rust**: 1.70+ (推荐使用最新稳定版)
- **VSCode**: 推荐的开发环境
- **Git**: 版本控制

### VSCode 扩展
- `rust-analyzer`: Rust 语言服务器
- `CodeLLDB`: 调试器支持
- `Better TOML`: Cargo.toml 语法高亮

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd c-disk-cleaner
```

### 2. 安装 Rust
访问 [https://rustup.rs/](https://rustup.rs/) 安装 Rust

### 3. 编译和运行

#### 开发模式
```bash
# 检查代码
cargo check

# 编译
cargo build

# 运行
cargo run
```

#### 发布模式
```bash
# 编译优化版本
cargo build --release

# 生成的 exe 文件位于 target/release/c-disk-cleaner.exe
```

### 4. VSCode 开发

在 VSCode 中打开项目后：
- 按 `Ctrl+Shift+P` 打开命令面板
- 输入 "Tasks: Run Task" 选择编译任务
- 或直接按 `Ctrl+F5` 运行程序
- 按 `F5` 启动调试模式

## 项目结构

```
c-disk-cleaner/
├── src/
│   ├── main.rs          # 程序入口点
│   ├── app.rs           # 主应用程序逻辑
│   ├── scanner.rs       # 磁盘扫描功能
│   ├── cleaner.rs       # 文件清理功能
│   └── utils.rs         # 工具函数
├── .vscode/
│   ├── tasks.json       # VSCode 任务配置
│   ├── launch.json      # VSCode 调试配置
│   └── settings.json    # VSCode 设置
├── Cargo.toml           # 项目配置和依赖
└── README.md           # 项目说明文档
```

## 核心模块说明

### scanner.rs - 磁盘扫描器
- `DiskScanner`: 主要的磁盘扫描类
- `ScanResult`: 扫描结果数据结构
- `FolderInfo`: 文件夹信息
- `FileInfo`: 文件信息

**主要功能**:
- 递归扫描指定目录
- 统计文件和文件夹大小
- 识别大文件
- 支持隐藏文件过滤
- 可配置扫描深度

### cleaner.rs - 磁盘清理器
- `DiskCleaner`: 主要的清理功能类
- `CleanupRule`: 清理规则定义
- `CleanupResult`: 清理结果

**主要功能**:
- 基于规则的文件清理
- 安全删除（移动到回收站）
- 批量文件操作
- 清理结果统计

### app.rs - 用户界面
- `DiskCleanerApp`: 主应用程序界面
- 多标签页界面设计
- 实时数据展示
- 用户交互处理

### utils.rs - 工具函数
- 文件大小格式化
- 时间格式化
- 安全性检查
- 磁盘空间查询

## 使用说明

### 磁盘分析
1. 在"扫描路径"中输入要分析的路径（默认 C:\）
2. 点击"🔍 开始扫描"按钮
3. 等待扫描完成，查看结果

### 文件清理
1. 切换到"🧹 清理工具"标签页
2. 选择要启用的清理规则
3. 点击对应的扫描按钮查看可清理文件
4. 确认后执行清理操作

### 设置配置
1. 切换到"⚙️ 设置"标签页
2. 配置扫描选项和过滤条件
3. 设置会自动保存

## 编译优化

发布版本使用了以下优化设置：
- `opt-level = 3`: 最高优化级别
- `lto = true`: 链接时优化
- `codegen-units = 1`: 单个代码生成单元
- `panic = "abort"`: 减小二进制文件大小
- `strip = true`: 移除调试符号

## 安全注意事项

1. **系统文件保护**: 工具会自动检测并保护重要系统文件
2. **安全模式**: 默认启用安全模式，删除的文件会移动到回收站
3. **权限检查**: 清理前会检查文件访问权限
4. **备份建议**: 重要数据请提前备份

## 故障排除

### 编译错误
- 确保 Rust 版本 >= 1.70
- 运行 `cargo clean` 清理缓存后重新编译
- 检查网络连接，确保能下载依赖包

### 运行时错误
- 检查是否有足够的磁盘权限
- 确保目标路径存在且可访问
- 查看日志输出获取详细错误信息

### 性能问题
- 大目录扫描可能需要较长时间，请耐心等待
- 可以通过设置最大扫描深度来限制扫描范围
- 发布版本性能比调试版本好很多

## 开发计划

- [ ] 实现文件夹选择对话框
- [ ] 添加可视化图表（饼图、柱状图）
- [ ] 实现异步扫描，避免界面卡顿
- [ ] 添加扫描进度显示
- [ ] 实现配置文件保存和加载
- [ ] 添加多语言支持
- [ ] 实现定时清理功能
- [ ] 添加文件恢复功能

## 许可证

MIT License - 详见 LICENSE 文件

## 贡献

欢迎提交 Issue 和 Pull Request！
