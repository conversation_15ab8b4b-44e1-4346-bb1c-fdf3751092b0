{"rustc": 3926191382657067107, "features": "[\"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"quote\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"quote\", \"test\", \"visit\", \"visit-mut\"]", "target": 11103975901103234717, "profile": 2225463790103693989, "path": 1063672758959911338, "deps": [[1988483478007900009, "unicode_ident", false, 8168668941875096033], [2713742371683562785, "build_script_build", false, 15262202955921880812], [3060637413840920116, "proc_macro2", false, 11783807764890264669], [17990358020177143287, "quote", false, 2178856385441165900]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-5ce57de5b222c0ea\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}