use std::path::{Path, PathBuf};
use std::fs;
use anyhow::{Result, Context};
use serde::{Deserialize, Serialize};

/// 磁盘清理器
pub struct DiskCleaner {
    /// 是否启用安全模式（移动到回收站而不是直接删除）
    safe_mode: bool,
    /// 清理规则
    rules: Vec<CleanupRule>,
}

/// 清理规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CleanupRule {
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: String,
    /// 目标路径模式
    pub path_patterns: Vec<String>,
    /// 文件扩展名过滤器
    pub file_extensions: Vec<String>,
    /// 最小文件年龄（天）
    pub min_age_days: Option<u32>,
    /// 是否启用此规则
    pub enabled: bool,
    /// 规则类型
    pub rule_type: CleanupRuleType,
}

/// 清理规则类型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum CleanupRuleType {
    /// 临时文件
    TempFiles,
    /// 浏览器缓存
    BrowserCache,
    /// 系统日志
    SystemLogs,
    /// 回收站
    RecycleBin,
    /// 下载文件夹
    Downloads,
    /// 自定义规则
    Custom,
}

/// 清理结果
#[derive(Debug, Clone)]
pub struct CleanupResult {
    /// 清理的文件数量
    pub files_cleaned: u64,
    /// 清理的文件夹数量
    pub folders_cleaned: u64,
    /// 释放的空间（字节）
    pub space_freed: u64,
    /// 清理耗时（毫秒）
    pub duration_ms: u64,
    /// 清理的文件列表
    pub cleaned_files: Vec<PathBuf>,
    /// 错误列表
    pub errors: Vec<String>,
}

impl DiskCleaner {
    /// 创建新的磁盘清理器
    pub fn new() -> Self {
        Self {
            safe_mode: true,
            rules: Self::default_rules(),
        }
    }
    
    /// 设置安全模式
    pub fn safe_mode(mut self, enabled: bool) -> Self {
        self.safe_mode = enabled;
        self
    }
    
    /// 获取默认清理规则
    fn default_rules() -> Vec<CleanupRule> {
        vec![
            CleanupRule {
                name: "Windows临时文件".to_string(),
                description: "清理Windows系统临时文件".to_string(),
                path_patterns: vec![
                    "%TEMP%".to_string(),
                    "%TMP%".to_string(),
                    "C:\\Windows\\Temp".to_string(),
                ],
                file_extensions: vec!["tmp".to_string(), "temp".to_string()],
                min_age_days: Some(1),
                enabled: true,
                rule_type: CleanupRuleType::TempFiles,
            },
            CleanupRule {
                name: "浏览器缓存".to_string(),
                description: "清理Chrome、Firefox等浏览器缓存".to_string(),
                path_patterns: vec![
                    "%LOCALAPPDATA%\\Google\\Chrome\\User Data\\Default\\Cache".to_string(),
                    "%APPDATA%\\Mozilla\\Firefox\\Profiles\\*\\cache2".to_string(),
                ],
                file_extensions: vec![],
                min_age_days: Some(7),
                enabled: false,
                rule_type: CleanupRuleType::BrowserCache,
            },
            CleanupRule {
                name: "系统日志文件".to_string(),
                description: "清理Windows系统日志文件".to_string(),
                path_patterns: vec![
                    "C:\\Windows\\Logs".to_string(),
                    "C:\\Windows\\System32\\LogFiles".to_string(),
                ],
                file_extensions: vec!["log".to_string(), "etl".to_string()],
                min_age_days: Some(30),
                enabled: false,
                rule_type: CleanupRuleType::SystemLogs,
            },
        ]
    }
    
    /// 扫描可清理的文件
    pub fn scan_cleanable_files(&self) -> Result<Vec<PathBuf>> {
        let mut cleanable_files = Vec::new();
        
        for rule in &self.rules {
            if !rule.enabled {
                continue;
            }
            
            for pattern in &rule.path_patterns {
                let expanded_path = self.expand_environment_variables(pattern)?;
                if let Ok(files) = self.scan_path_with_rule(&expanded_path, rule) {
                    cleanable_files.extend(files);
                }
            }
        }
        
        Ok(cleanable_files)
    }
    
    /// 根据规则扫描路径
    fn scan_path_with_rule(&self, path: &str, rule: &CleanupRule) -> Result<Vec<PathBuf>> {
        let mut files = Vec::new();
        let path = Path::new(path);
        
        if !path.exists() {
            return Ok(files);
        }
        
        if path.is_file() {
            if self.file_matches_rule(path, rule)? {
                files.push(path.to_path_buf());
            }
        } else if path.is_dir() {
            for entry in fs::read_dir(path)? {
                let entry = entry?;
                let entry_path = entry.path();
                
                if entry_path.is_file() {
                    if self.file_matches_rule(&entry_path, rule)? {
                        files.push(entry_path);
                    }
                } else if entry_path.is_dir() {
                    // 递归扫描子目录
                    if let Ok(sub_files) = self.scan_path_with_rule(
                        &entry_path.to_string_lossy(),
                        rule
                    ) {
                        files.extend(sub_files);
                    }
                }
            }
        }
        
        Ok(files)
    }
    
    /// 检查文件是否匹配规则
    fn file_matches_rule(&self, path: &Path, rule: &CleanupRule) -> Result<bool> {
        // 检查文件扩展名
        if !rule.file_extensions.is_empty() {
            if let Some(ext) = path.extension() {
                let ext_str = ext.to_string_lossy().to_lowercase();
                if !rule.file_extensions.iter().any(|e| e.to_lowercase() == ext_str) {
                    return Ok(false);
                }
            } else {
                return Ok(false);
            }
        }
        
        // 检查文件年龄
        if let Some(min_age_days) = rule.min_age_days {
            let metadata = fs::metadata(path)?;
            if let Ok(modified) = metadata.modified() {
                let age = std::time::SystemTime::now()
                    .duration_since(modified)
                    .unwrap_or_default();
                
                if age.as_secs() < (min_age_days as u64 * 24 * 60 * 60) {
                    return Ok(false);
                }
            }
        }
        
        Ok(true)
    }
    
    /// 执行清理
    pub fn cleanup(&self, files: &[PathBuf]) -> Result<CleanupResult> {
        let start_time = std::time::Instant::now();
        let mut result = CleanupResult {
            files_cleaned: 0,
            folders_cleaned: 0,
            space_freed: 0,
            duration_ms: 0,
            cleaned_files: Vec::new(),
            errors: Vec::new(),
        };
        
        for file_path in files {
            match self.delete_file(file_path) {
                Ok(size) => {
                    result.files_cleaned += 1;
                    result.space_freed += size;
                    result.cleaned_files.push(file_path.clone());
                    log::info!("已清理文件: {:?}", file_path);
                }
                Err(e) => {
                    let error_msg = format!("清理文件失败 {:?}: {}", file_path, e);
                    result.errors.push(error_msg);
                    log::warn!("清理文件失败: {:?}, 错误: {}", file_path, e);
                }
            }
        }
        
        result.duration_ms = start_time.elapsed().as_millis() as u64;
        
        log::info!(
            "清理完成: {} 个文件, 释放空间 {} 字节, 耗时 {} 毫秒",
            result.files_cleaned, result.space_freed, result.duration_ms
        );
        
        Ok(result)
    }
    
    /// 删除单个文件
    fn delete_file(&self, path: &Path) -> Result<u64> {
        let metadata = fs::metadata(path)
            .with_context(|| format!("无法获取文件元数据: {:?}", path))?;
        
        let file_size = metadata.len();
        
        if self.safe_mode {
            // 安全模式：移动到回收站
            self.move_to_recycle_bin(path)?;
        } else {
            // 直接删除
            fs::remove_file(path)
                .with_context(|| format!("无法删除文件: {:?}", path))?;
        }
        
        Ok(file_size)
    }
    
    /// 移动文件到回收站
    #[cfg(windows)]
    fn move_to_recycle_bin(&self, path: &Path) -> Result<()> {
        // 在Windows上使用Shell API移动到回收站
        // 这里是简化实现，实际应该使用Windows API
        log::warn!("回收站功能尚未实现，直接删除文件: {:?}", path);
        fs::remove_file(path)
            .with_context(|| format!("无法删除文件: {:?}", path))?;
        Ok(())
    }
    
    /// 移动文件到回收站（非Windows平台）
    #[cfg(not(windows))]
    fn move_to_recycle_bin(&self, path: &Path) -> Result<()> {
        // 非Windows平台的回收站实现
        fs::remove_file(path)
            .with_context(|| format!("无法删除文件: {:?}", path))?;
        Ok(())
    }
    
    /// 展开环境变量
    fn expand_environment_variables(&self, path: &str) -> Result<String> {
        let mut expanded = path.to_string();
        
        // 简单的环境变量展开
        if expanded.contains("%TEMP%") {
            if let Ok(temp_dir) = std::env::var("TEMP") {
                expanded = expanded.replace("%TEMP%", &temp_dir);
            }
        }
        
        if expanded.contains("%TMP%") {
            if let Ok(tmp_dir) = std::env::var("TMP") {
                expanded = expanded.replace("%TMP%", &tmp_dir);
            }
        }
        
        if expanded.contains("%LOCALAPPDATA%") {
            if let Ok(local_appdata) = std::env::var("LOCALAPPDATA") {
                expanded = expanded.replace("%LOCALAPPDATA%", &local_appdata);
            }
        }
        
        if expanded.contains("%APPDATA%") {
            if let Ok(appdata) = std::env::var("APPDATA") {
                expanded = expanded.replace("%APPDATA%", &appdata);
            }
        }
        
        Ok(expanded)
    }
    
    /// 获取清理规则
    pub fn get_rules(&self) -> &[CleanupRule] {
        &self.rules
    }
    
    /// 更新清理规则
    pub fn update_rule(&mut self, index: usize, rule: CleanupRule) -> Result<()> {
        if index < self.rules.len() {
            self.rules[index] = rule;
            Ok(())
        } else {
            Err(anyhow::anyhow!("规则索引超出范围"))
        }
    }
}

impl Default for DiskCleaner {
    fn default() -> Self {
        Self::new()
    }
}
