use eframe::egui;
use std::sync::{Arc, Mutex};

mod app;
mod scanner;
mod cleaner;
mod utils;

use app::DiskCleanerApp;

fn main() -> Result<(), eframe::Error> {
    // 初始化日志
    env_logger::init();
    
    // 设置窗口选项
    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([1200.0, 800.0])
            .with_min_inner_size([800.0, 600.0])
            .with_icon(
                // 可以在这里添加应用图标
                eframe::icon_data::from_png_bytes(&[]).unwrap_or_default(),
            ),
        ..Default::default()
    };

    // 启动应用
    eframe::run_native(
        "C盘清理工具",
        options,
        Box::new(|cc| {
            // 设置字体以支持中文
            setup_custom_fonts(&cc.egui_ctx);
            
            Box::new(DiskCleanerApp::new(cc))
        }),
    )
}

fn setup_custom_fonts(ctx: &egui::Context) {
    let mut fonts = egui::FontDefinitions::default();
    
    // 添加中文字体支持
    fonts.font_data.insert(
        "my_font".to_owned(),
        egui::FontData::from_static(include_bytes!("../assets/fonts/NotoSansCJK-Regular.ttc")),
    );
    
    // 如果没有字体文件，使用系统默认字体
    fonts
        .families
        .entry(egui::FontFamily::Proportional)
        .or_default()
        .insert(0, "my_font".to_owned());
    
    fonts
        .families
        .entry(egui::FontFamily::Monospace)
        .or_default()
        .push("my_font".to_owned());
    
    ctx.set_fonts(fonts);
}
